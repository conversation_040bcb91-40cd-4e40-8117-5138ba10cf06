#!/usr/bin/env python3
"""
Bug召回功能测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.bug_recall import BugRecallTool
from utils.logger.logger import Logging

logger = Logging().get_logger()

def test_bug_recall():
    """测试bug召回功能"""
    print("=" * 50)
    print("开始测试Bug召回功能")
    print("=" * 50)
    
    # 初始化bug召回工具
    bug_tool = BugRecallTool()
    
    # 测试需求链接（请替换为实际的TAPD链接）
    test_urls = [
        # 请在这里添加实际的TAPD需求链接进行测试
        # "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503",
        # "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919229",
    ]

    if not test_urls:
        print("⚠️  请在test_urls中添加实际的TAPD需求链接进行测试")
        return
    
    for i, story_url in enumerate(test_urls, 1):
        print(f"\n测试用例 {i}: {story_url}")
        print("-" * 30)
        
        try:
            result = bug_tool.recall_bugs_by_story_url(story_url)
            
            if result:
                print("✅ Bug召回成功!")
                print(f"需求名称: {result['story_info']['name']}")
                print(f"需求分类: {result['story_info']['category']}")
                print(f"相关Bug数量: {len(result['relevant_bugs'])}")
                print(f"建议测试用例数量: {len(result['suggested_test_cases'])}")
                print(f"结果文件: {result.get('file_path', '未生成')}")
                
                # 显示相关Bug摘要
                if result['relevant_bugs']:
                    print("\n相关Bug摘要:")
                    for bug in result['relevant_bugs'][:3]:  # 只显示前3个
                        print(f"  - Bug ID: {bug.get('bug_id', 'N/A')}")
                        print(f"    标题: {bug.get('title', 'N/A')}")
                        print(f"    相关性评分: {bug.get('relevance_score', 'N/A')}")
                        print(f"    风险影响: {bug.get('risk_impact', 'N/A')}")
                
                # 显示测试用例摘要
                if result['suggested_test_cases']:
                    print("\n建议测试用例摘要:")
                    for case in result['suggested_test_cases'][:2]:  # 只显示前2个
                        print(f"  - 用例标题: {case.get('test_case_title', 'N/A')}")
                        print(f"    测试目标: {case.get('test_objective', 'N/A')}")
                        print(f"    优先级: {case.get('priority', 'N/A')}")
                        print(f"    测试类型: {case.get('test_type', 'N/A')}")
                
            else:
                print("❌ Bug召回失败")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            logger.error(f"测试Bug召回功能时发生错误: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Bug召回功能测试完成")
    print("=" * 50)

def test_tapd_bug_api():
    """测试TAPD Bug API"""
    print("\n" + "=" * 50)
    print("测试TAPD Bug API")
    print("=" * 50)
    
    from utils.tapd import TAPDUtils
    
    tapd_utils = TAPDUtils()
    
    # 测试需求链接（请替换为实际链接）
    story_url = "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503"

    print(f"测试链接: {story_url}")
    print("⚠️  请确保该链接是有效的TAPD需求链接")
    
    try:
        # 测试获取需求信息
        print("1. 测试获取需求信息...")
        story = tapd_utils.get_story(story_url)
        if story:
            print(f"✅ 需求获取成功: {story.get('name', 'N/A')}")
        else:
            print("❌ 需求获取失败")
            return
        
        # 测试获取关联Bug
        print("\n2. 测试获取关联Bug...")
        bugs = tapd_utils.get_bugs_by_story_url(story_url)
        if bugs:
            print(f"✅ 找到 {len(bugs)} 个关联Bug")
            for bug in bugs[:3]:  # 只显示前3个
                print(f"  - Bug ID: {bug.get('id', 'N/A')}")
                print(f"    标题: {bug.get('title', 'N/A')}")
                print(f"    状态: {bug.get('status', 'N/A')}")
                print(f"    严重程度: {bug.get('severity', 'N/A')}")
        else:
            print("⚠️  未找到关联Bug")
            
    except Exception as e:
        print(f"❌ TAPD API测试失败: {str(e)}")
        logger.error(f"测试TAPD Bug API时发生错误: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    test_tapd_bug_api()
    test_bug_recall()
