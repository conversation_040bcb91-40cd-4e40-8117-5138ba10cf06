#!/usr/bin/env python3
"""
测试基于TRAG向量搜索的Bug召回功能
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.bug_recall import BugRecallTool
from utils.logger.logger import Logging

logger = Logging().get_logger()

def test_bug_recall_trag():
    """测试TRAG向量搜索Bug召回功能"""
    
    print("=" * 60)
    print("🚀 测试基于TRAG向量搜索的Bug召回功能")
    print("=" * 60)
    
    # 检查环境变量配置
    bug_namespace = os.getenv("BUG_NAMESPACE")
    bug_collection = os.getenv("BUG_COLLECTION")
    
    print(f"📋 环境配置检查:")
    print(f"  - BUG_NAMESPACE: {bug_namespace or '未设置'}")
    print(f"  - BUG_COLLECTION: {bug_collection or '未设置'}")
    
    if not bug_namespace or not bug_collection:
        print("\n⚠️  警告: Bug知识库环境变量未设置")
        print("请设置以下环境变量:")
        print("  export BUG_NAMESPACE=your_bug_namespace")
        print("  export BUG_COLLECTION=your_bug_collection")
        print("\n将使用默认搜索方式进行测试...")
    
    # 初始化Bug召回工具
    try:
        bug_tool = BugRecallTool(bug_namespace=bug_namespace, bug_collection=bug_collection)
        print(f"\n✅ Bug召回工具初始化成功")
    except Exception as e:
        print(f"\n❌ Bug召回工具初始化失败: {str(e)}")
        return False
    
    # 测试需求链接
    test_story_urls = [
        "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503",
        # 可以添加更多测试链接
    ]
    
    for i, story_url in enumerate(test_story_urls, 1):
        print(f"\n{'='*40}")
        print(f"📝 测试用例 {i}: {story_url}")
        print(f"{'='*40}")
        
        try:
            # 执行Bug召回
            result = bug_tool.recall_bugs_by_story_url(story_url)
            
            if result:
                print(f"✅ Bug召回成功!")
                
                # 显示基本信息
                story_info = result.get('story_info', {})
                print(f"📋 需求信息:")
                print(f"  - 名称: {story_info.get('name', '未知')}")
                print(f"  - 分类: {story_info.get('category', '未知')}")
                print(f"  - 搜索方法: {story_info.get('search_method', '未知')}")
                
                # 显示搜索统计
                stats = result.get('search_stats', {})
                print(f"\n📊 搜索统计:")
                print(f"  - TRAG搜索到: {stats.get('total_searched', 0)} 个Bug")
                print(f"  - TRAG筛选后: {stats.get('trag_filtered', 0)} 个Bug")
                print(f"  - LLM筛选后: {stats.get('llm_filtered', 0)} 个Bug")
                print(f"  - 生成测试用例: {stats.get('final_count', 0)} 个")
                
                # 显示相关Bug
                relevant_bugs = result.get('relevant_bugs', [])
                print(f"\n🐛 相关Bug ({len(relevant_bugs)} 个):")
                for j, bug in enumerate(relevant_bugs[:3], 1):  # 只显示前3个
                    title = bug.get('title', '未知标题')
                    trag_score = bug.get('trag_relevance_score', 0)
                    llm_score = bug.get('relevance_score', 0)
                    print(f"  {j}. {title}")
                    print(f"     TRAG评分: {trag_score:.2f}, LLM评分: {llm_score}")
                
                # 显示测试用例
                test_cases = result.get('suggested_test_cases', [])
                print(f"\n📝 建议测试用例 ({len(test_cases)} 个):")
                for j, case in enumerate(test_cases[:3], 1):  # 只显示前3个
                    title = case.get('title', '未知标题')
                    priority = case.get('priority', '未知')
                    print(f"  {j}. {title} (优先级: {priority})")
                
                # 显示文件路径
                file_path = result.get('file_path', '')
                if file_path:
                    print(f"\n📁 结果文件: {file_path}")
                    if os.path.exists(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"   文件大小: {file_size} 字节")
                    else:
                        print(f"   ⚠️ 文件不存在")
                
            else:
                print(f"❌ Bug召回失败")
                
        except Exception as e:
            print(f"❌ 测试过程中发生错误: {str(e)}")
            logger.error(f"测试Bug召回时发生错误: {str(e)}")
    
    print(f"\n{'='*60}")
    print("🎉 Bug召回功能测试完成")
    print("=" * 60)
    
    return True

def test_trag_connection():
    """测试TRAG连接"""
    print("\n🔍 测试TRAG连接...")
    
    try:
        from rag_server.trag_manager import TRAGManager
        
        bug_namespace = os.getenv("BUG_NAMESPACE")
        bug_collection = os.getenv("BUG_COLLECTION")
        
        if bug_namespace and bug_collection:
            trag_manager = TRAGManager(ns_code=bug_namespace, coll_code=bug_collection)
            print(f"✅ TRAG连接成功: {bug_namespace}/{bug_collection}")
            
            # 测试搜索
            test_query = "登录功能"
            results = trag_manager.search_test_cases(query=test_query, limit=5)
            print(f"🔍 测试搜索 '{test_query}': 找到 {len(results)} 个结果")
            
        else:
            print("⚠️ Bug知识库环境变量未设置，跳过TRAG连接测试")
            
    except Exception as e:
        print(f"❌ TRAG连接测试失败: {str(e)}")

if __name__ == "__main__":
    print("🧪 开始Bug召回功能测试")
    
    # 测试TRAG连接
    test_trag_connection()
    
    # 测试Bug召回功能
    success = test_bug_recall_trag()
    
    if success:
        print("\n✅ 所有测试完成")
    else:
        print("\n❌ 测试过程中出现问题")
        sys.exit(1)
