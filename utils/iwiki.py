import requests
from trag_tools_sdk.function_loader import load_functions
import os
import hashlib
import requests as req
import time
import random
from dotenv import load_dotenv
from utils.logger.logger import Logging

logger = Logging().get_logger()

load_dotenv()
class IwikiToMdConverter:
    def __init__(self):
        """初始化转换器，加载必要的函数执行器"""
        self.iwiki_token = os.getenv("IWIKI_TOKEN")
        self.iwiki_pass_id = os.getenv("IWIKI_PASS_ID")
        self.api_key = os.getenv("TRAG_TOKEN")
        self.iwiki_executor = load_functions("doc_format_transformer", "iwiki2md", api_key=self.api_key)
        self.import_executor = load_functions("index_query_tools", "import_files", api_key=self.api_key)

    def _download_md_content(self, url):
        """
        从给定的URL下载MD文件内容

        Args:
            url (str): MD文件的URL

        Returns:
            str: MD文件内容
        """
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.text
            return f"Error downloading MD content: {response.status_code}"
        except Exception as e:
            return f"Error downloading MD content: {str(e)}"

    def convert(self, iwiki_url):
        """
        将iwiki页面转换为MD格式字符串

        Args:
            iwiki_url (str): iwiki页面URL

        Returns:
            str: 转换后的MD内容字符串
        """

        data = {
            # "extract_image_info": "",
            # 到iwiki指定控件
            "iwiki_token": self.iwiki_token,
            "iwiki_pass_id": self.iwiki_pass_id,
            "iwiki_urls": [iwiki_url],
        }
        result = self.iwiki_executor.run("iwiki2md", **data)
        print(result)
        if result['code'] != 0:
            logger.error(f"Failed to convert iwiki: {result.get('message', 'Unknown error')}")
            return ""
        md_list = result.get('md', [])
        if not md_list:
            logger.error("No MD content found in the conversion result.")
            return ""
        md_item = md_list[0]  # 假设只处理第一个结果
        return self._download_md_content(md_item['url'])

class IwikiKnowledgeImporter:
    def __init__(self):
        """初始化导入器，加载必要的配置和执行器"""
        self.iwiki_token = os.getenv("IWIKI_TOKEN")
        self.iwiki_pass_id = os.getenv("IWIKI_PASS_ID")
        self.api_key = os.getenv("TRAG_TOKEN")

        # TRAG配置
        self.rag_code = os.getenv("RAG_CODE")
        self.ns_code = os.getenv("SASS_NAMESPACE")
        self.col_code = os.getenv("SAAS_STORY_COLL")
        self.import_policy = os.getenv("TRAG_IMPORT_POLICY", "public-default-import")

        # 加载执行器
        self.iwiki_executor = load_functions("file_format_transformer", "iwiki2md", api_key=self.api_key)
        self.import_executor = load_functions("index_query_tools", "import_files", api_key=self.api_key)

    def import_from_iwiki(self, iwiki_ids: list[str]):
        """
        从iwiki导入文档到TRAG知识库

        Args:
            iwiki_ids (List[str]): iwiki页面ID列表

        Returns:
            dict: 导入结果
        """
        # 将ID列表转换为完整URL列表
        iwiki_urls = [f"https://iwiki.woa.com/p/{id}" for id in iwiki_ids]

        # 下载iwiki文档并转换为MD格式
        download_data = {
            "iwiki_token": self.iwiki_token,
            "iwiki_pass_id": self.iwiki_pass_id,
            "iwiki_urls": iwiki_urls,
        }
        download_result = self.iwiki_executor.run("iwiki2md", **download_data)

        if download_result["code"] != 0:
            logger.error(f"下载iwiki文档失败: {download_result.get('message', '未知错误')}")
            return None

        # 准备导入数据
        md_list = download_result["md"]
        files = [{"type": "url", "content": md_item["url"]} for md_item in md_list]

        import_data = {
            "policy": self.import_policy,
            "namespace_code": self.ns_code,
            "rag_code": self.rag_code,
            "collection_code": self.col_code,
            "files": files,
        }

        # 执行导入
        return self.import_executor.run("import_files", **import_data)

# # 示例用法
# converter = IwikiToMdConverter()
# md_content = converter.convert("https://iwiki.woa.com/p/4013779284")
# print(md_content)


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import binascii
import hashlib
import os
import sys
import time
import json
import logging
import logging.handlers
import requests
from requests.auth import AuthBase


def get_logger():
    fmt = '%(asctime)s %(levelname)s[%(filename)s:%(lineno)d][%(funcName)s] %(message)s'
    fmt = logging.Formatter(fmt, '%Y-%m-%d %H:%M:%S')

    folder = os.path.join(os.getcwd(), 'logs')
    if not os.path.exists(folder):
        os.mkdir(folder)
    path = os.path.join(folder, 'iwiki.log')

    file_handler = logging.handlers.TimedRotatingFileHandler(
        path, when='D', backupCount=90, encoding='utf-8'
    )
    file_handler.setFormatter(fmt)
    logger = logging.getLogger('root')

    stream_handler = logging.StreamHandler(sys.stderr)
    stream_handler.setFormatter(fmt)
    logger.addHandler(file_handler)
    logger.addHandler(stream_handler)
    logger.setLevel('DEBUG')
    logger.propagate = False
    return logger


def hexlify(s):
    return binascii.hexlify(s).decode('utf-8')


def calc_signature(token, timestamp, nonce):
    s = u'{timestamp}{token}{nonce}{timestamp}'.format(timestamp=timestamp, token=token, nonce=nonce)
    s = hashlib.sha256(s.encode('utf-8')).digest()
    return hexlify(s)


def prepare_rio_headers(paasid, token):
    timestamp = str(int(time.time()))
    nonce = hexlify(os.urandom(16))
    signature = calc_signature(token, timestamp, nonce)
    return {
        'x-rio-paasid': paasid,
        'x-rio-signature': signature,
        'x-rio-timestamp': timestamp,
        'x-rio-nonce': nonce,
    }


class RioHeaderAuth(AuthBase):
    def __init__(self, paasid, token):
        self.paasid = paasid
        self.token = token

    def __call__(self, r):
        headers = prepare_rio_headers(self.paasid, self.token)
        r.headers.update(headers)
        return r


def dump(rsp):
    print()
    print('status:', rsp.status_code)
    print('headers:')
    for k, v in rsp.headers.items():
        print(k, v)
    print()
    print('body:')
    print(rsp.text)


class ApiSession(requests.Session):
    def __init__(self, base=None, *args, **kwargs):
        super(ApiSession, self).__init__(*args, **kwargs)
        self._base = base.rstrip('/')

    def request(self, method, url, *args, **kwargs):
        if url.startswith('/'):
            url = self._base + url
        return super(ApiSession, self).request(method, url, *args, **kwargs)


class Wiki(object):
    @classmethod
    def from_env(cls):
        kwargs = {
            'env': os.getenv('env', 'devnet'),
            'paasid': os.getenv('paasid', ''),
            'token': os.getenv('token', ''),
        }
        return Wiki(**kwargs)

    @classmethod
    def from_file(cls, path):
        with open(path, 'r') as fp:
            data = fp.read()
        kwargs = {}
        if path.endswith('.json'):
            kwargs = json.loads(data)
        else:
            for line in data.splitlines(keepends=False):
                args = line.split('=', maxsplit=2)
                if line.startswith('#') or len(args) != 2:
                    continue
                kwargs[args[0]] = args[1]
        kwargs.setdefault('env', 'devnet')
        return Wiki(**kwargs)

    def __init__(self, env, paasid, token):
        regions = {
            'oa':     'http://api-g.sgw.woa.com',
            'oss':    'http://api-idc.sgw.woa.com',
            'idc':    'http://api-g-idc.sgw.woa.com',
            'idcw':   'http://api-g-idcw.sgw.woa.com',
            'devnet': 'http://api-idc.sgw.woa.com',
            'test':   'http://test.devnet.rio.tencent.com',
        }
        base = regions.get(env, None)
        if not base:
            regions = "/".join(regions.keys())
            raise ValueError(f'invalid env "{env}", must be one of {regions}, suggest you try env devnet/idc')
        self.base = f'{regions[env]}/ebus/iwiki/prod'
        s = ApiSession(self.base)
        # s.trust_env = False
        s.auth = RioHeaderAuth(paasid, token)
        self.client = s

    def page_link(self, id, title=None):
        url = f'https://iwiki.woa.com/p/{id}'
        return f'[{title}]({url})' if title else url

    def get_json(self, api, params=None):
        rsp = self.client.get(api, params=params)
        return rsp.json()

    def post_json(self, api, body, params=None):
        rsp = self.client.post(api, json=body, params=params)
        return rsp.json()

    def ping(self):
        return self.client.get('/tencent/api/v2/user/current')

    def get_user_info(self):
        return self.get_json('/tencent/api/v2/user/current')['data']

    def get_doc_body(self, id, body_mode='md'):
        return self.get_json(f'/tencent/api/v2/doc/body?id={id}&bodymode={body_mode}')

    def get_space(self, k):
        try:
            rsp = self.client.get(f'/tencent/api/space/{k}')
            if rsp.status_code == 404:
                raise Exception(f'Space "{k}" not found (404)')

            data = rsp.json()
            if 'result' not in data:
                raise Exception(f'Invalid API response format for space "{k}": missing "result" field')

            return data['result']
        except Exception as e:
            msg = f'Failed to get space "{k}": {str(e)}'
            logger.error(msg)
            logger.debug(f'API Response: {rsp.text if "rsp" in locals() else "N/A"}')
            raise Exception(msg)

    def children(self, id):
        try:
            data = self.get_json(f'/tencent/api/v2/pagetree/children?parentid={id}')
            return data['data']['list']
        except Exception as e:
            msg = f'get children of page "{id}" failed: {e}'
            logger.exception(msg)
            raise Exception(msg)

    def find_children(self, id, title):
        children = self.children(id)
        return [i['id'] for i in children if i['title'] == title]

    def mkdir(self, parent, title):
        body = {
            'parentid': parent,
            'name': title,
        }
        try:
            data = self.post_json('/tencent/api/v2/doc/folder/create', body)
            return data['data']['id']
        except Exception as e:
            msg = f'mkdir {title} under {parent} failed: {e}'
            logger.exception(msg)
            raise Exception(msg)

    def create_doc(self, parent_id, title, body, content_type):
        body = {
            'parentid': parent_id,
            'contenttype': content_type,
            'title': title,
            'body': body,
        }
        try:
            data = self.post_json('/tencent/api/v2/doc/create', body)
            return data['data']['id']
        except Exception as e:
            msg = f'create_doc {parent_id} / {title} failed: {e}'
            logger.exception(msg)
            raise Exception(msg)

    def update_doc(self, id, title, body):
        body = {
            'id': id,
            'title': title,
            'body': body,
            'version': 1,
            'force': True,
        }
        try:
            data = self.post_json('/tencent/api/v2/doc/save', body)
            if data['msg'] != 'ok':
                raise Exception('update_doc failed: ' + data['msg'])
        except Exception as e:
            msg = f'update_doc "{title}"({id}) failed: {e}'
            logger.exception(msg)
            raise Exception(msg)

    def upload_attachment(self, doc_id, path, name=None):
        if not name:
            name = os.path.basename(path)
        api = f'/tencent/api/attachments/s3/presign?docid={doc_id}'
        rsp = self.get_json(api)
        attachmentid = rsp['data']['attachmentid']
        url = rsp['data']['url']
        rsp = self.client.put(url, data=open(path, 'rb'))
        ok = rsp.status_code == 200
        body = {
            'attachmentid': attachmentid,
            'status': ok and 'ok' or 'failure',
            'filepath': name
        }
        data = self.post_json('/tencent/api/attachments/notify', body)
        if not ok:
            raise ValueError(f'upload failed: {rsp.text}')
        if data['msg'] != 'ok':
            raise ValueError(f'upload notify failed: {data["msg"]}')
        return attachmentid

    def get_all_doc_ids(self, parent_id):
        """
        递归获取目录下的所有文档ID。

        Args:
            parent_id (int): 父文档ID。

        Returns:
            list: 所有文档ID的列表。
        """
        doc_ids = []
        try:
            children = self.children(parent_id)
            for child in children:
                doc_ids.append(child['id'])
                if child.get('has_children', False):
                    doc_ids.extend(self.get_all_doc_ids(child['id']))
            return doc_ids
        except Exception as e:
            logger.error(f'Failed to get all doc ids under {parent_id}: {e}')
            raise


if __name__ == '__main__':
    '''
    wiki = Wiki('devnet', 'your-paasid', 'your-token')
    or
    wiki = Wiki.from_env('rio.env')
    # add rio.env file:
    paasid=your-paasid
    token=your-token
    env=devnet
    '''
    logger = get_logger()
    wiki = Wiki.from_file('../../rio.env')


    # 验证连接
    print(wiki.ping().text)


    # 获取当前app key用户信息
    user = wiki.get_user_info()
    print('用户信息:', user['name_en'])


    # 获取空间信息（个人空间key为 '~英文名'）
    personal_space = 'TSEP'
    space = wiki.get_space(personal_space)
    homepage_id = space['homepageid']
    print('空间信息:', space)
    print('空间首页ID:', homepage_id)
    iwiki_ids = wiki.get_all_doc_ids(1295609484)
    print("获取所有文档 id:", iwiki_ids)

    IwikiKnowledgeImporter().import_from_iwiki(iwiki_ids)

    # print('获取文档内容:', wiki.get_doc_body(1860545057)['data']['body'])
    #
    # print('获取目录:', wiki.children(1295609484))

    # pages = wiki.find_children(homepage_id, 'test')
    # print('在某个文档下一级找某个标题的文档:', pages)
    # if pages:
    #     parent = pages[0]
    #     new_id = wiki.create_doc(parent, 'test', '# 123\n## 456', 'MD')
    #     # new_id = wiki.create_doc(parent, 'test', '<h1>test</h1><p>123</p>', 'DOC')
    #     print('创建文档:', new_id, wiki.page_link(new_id))
    #     wiki.update_doc(new_id, 'test2', '# 111\n## 222')

        # 附件
        # attachmentid = wiki.upload_attachment(new_id, 'demo.json')
        # print('附件地址', f'/tencent/api/attachments/s3/url?attachmentid={attachmentid}')
