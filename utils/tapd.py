# html_processor.py
from bs4 import BeautifulSoup
import html2text
import re
import json
from utils.logger.logger import Logging
from utils.llm.chat import llm_vision

logger = Logging().get_logger()
class HTMLProcessor:
    def __init__(self, excluded_tags=None, markdown_config=None):
        self.excluded_tags = excluded_tags or ['script', 'style', 'nav']
        self.markdown_config = markdown_config or {'body_width': 0}

    def clean_html(self, html):
        """清理指定标签的HTML文档"""
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup(self.excluded_tags):
            tag.decompose()
        return str(soup)

    def convert_to_markdown(self, html):
        """转换为Markdown格式"""
        converter = html2text.HTML2Text()
        converter.body_width = self.markdown_config.get('body_width', 0)
        return converter.handle(self.clean_html(html))


import requests
from requests.exceptions import RequestException
from utils.iwiki import IwikiToMdConverter

class TAPDClient:
    def __init__(self, base_url='http://oss.apiv2.tapd.woa.com', auth_token=None):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Basic {auth_token}' if auth_token else ''
        }

    def get_story_name(self, workspace_id, story_id):
        """获取 tapd需求名称"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'name'
        }

        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            logger.info(f"获取需求:{data}")
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('name')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e
    
    def get_story(self, workspace_id, story_id):
        """获取 tapd需求全部信息"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'id,name,status,owner,description,category_id'       
            }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_story_description(self, workspace_id, story_id):
        """获取 tapd需求详情"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'description'
        }

        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('description')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_image(self, workspace_id, image_path):
        """
        获取TAPD图片
        参数:
            workspace_id: 工作区ID
            image_path: 图片路径(如/tfl/captures/2023-07/tapd_10104801_base64_1689686020_146.png)
        返回:
            requests.Response对象
        """
        params = {
            'workspace_id': workspace_id,
            'image_path': image_path
        }

        try:
            response = requests.get(
                f"{self.base_url}/files/get_image",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            response = response.json()
            # logger.info(f"获取图片: {response}")
            download_url = response.get('data', {}).get('Attachment', {}).get('download_url', "")
            return download_url
        except RequestException as e:
            raise TAPDError(f"获取图片失败: {str(e)}") from e

    def get_story_category(self, workspace_id, id):
        """根据分类 id获取 TAPD 需求分类名称"""
        params = {
            'workspace_id': int(workspace_id),
            'id': int(id)
        }
        try:
            response = requests.get(
                f"{self.base_url}/story_categories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")

            # 修复点：检查data.get('data')是否为空
            category_data = data.get('data', [{}])
            if not category_data:  # 如果列表为空
                logger.warning(f"未找到分类ID {id} 对应的数据")
                return ""

            return category_data[0].get('Category', {}).get('name')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e



    def get_bug_detail(self, workspace_id, bug_id):
        """获取bug详细信息"""
        params = {
            'workspace_id': workspace_id,
            'id': bug_id,
            'fields': 'id,title,description,severity,priority,status,reporter,owner,created,modified,category_id'
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")

            bug_data = data.get('data', [{}])[0].get('Bug', {})
            return bug_data
        except RequestException as e:
            raise TAPDError(f"获取Bug详情失败: {str(e)}") from e



class TAPDUtils:
    """TAPD 相关操作工具类，负责处理 TAPD URL 解析和数据获取"""

    def __init__(self):
        self.config = AppConfig()
        self.client = TAPDClient(auth_token=self.config.auth_token)
        self.processor = HTMLProcessor(
            excluded_tags=self.config.excluded_tags,
            markdown_config=self.config.markdown_config
        )

    def get_story_name(self, url):
        """
        从 TAPD URL 中获取需求名称
        :param url: TAPD 需求 URL
        :return: 需求名称，如果 URL 无效则返回 None
        """
        match = self.match_url(url)
        if match:
            workspace_id, story_id = match.groups()
            return self.client.get_story_name(workspace_id, story_id)
        return None

    def get_story_description(self, url):
        """
        从 TAPD URL 中获取需求描述（Markdown 格式）
        :param url: TAPD 需求 URL
        :return: 需求描述（Markdown 格式），如果 URL 无效则返回 None
        """
        match = self.match_url(url)
        if match:
            workspace_id, story_id = match.groups()
            description = self.client.get_story_description(workspace_id, story_id)
            return self.processor.convert_to_markdown(description)
        return None
    def _match_iwiki_url_id(self, data):
        """
        匹配 iWiki URL 
        :param url: iWiki 需求 URL
        :return: 匹配结果（re.Match 对象），如果 URL 无效则返回 None
        """
        match = re.search(r'iwiki.woa.com/p/(\d+)', data)
        if match:
            doc_id = match.group(1)
            logger.info(f"匹配到 iWiki URL ID: {doc_id}")
            return doc_id
        else:
            logger.info("该需求未匹配到 iWiki URL ID")
            return None
    def _extract_image_links(self,text):
        """
        从文本中提取所有符合要求的图片链接
        参数:
            text: 要搜索的文本字符串
        返回:
            匹配到的图片链接列表
        """
        # 正则表达式匹配以/tfl开头，以.png/.jpg/.jpeg结尾的链接
        pattern = r'/tfl[^\s]*?\.(?:png|jpg|jpeg)'
        matches = re.findall(pattern, text, re.IGNORECASE)
        return matches
    def _get_img_download_url(require_url,img_url):
        tapd_utils = TAPDUtils()
        img_url = tapd_utils.get_img_download_url(require_url,img_url)
        return img_url

        
    def get_story(self, story_url):
        """
        从 TAPD URL 中获取完整需求信息
        :param url: TAPD 需求 URL
        :return: 需求字典（包含描述等字段），如果 URL 无效则返回 None
        """
        match = self.match_url(story_url)
        if match:
            workspace_id, story_id = match.groups()
            story = self.client.get_story(workspace_id, story_id)
            logger.info(f"获取原始需求: {story}")
            story_str = json.dumps(story, ensure_ascii=False)
            if story:
                # 处理 description 为 None 的情况
                des = story.get('description', '')
                if des is None:
                    des = ''
                story['description'] = self.processor.convert_to_markdown(des)
                logger.info(f"需求描述转 markdown结果: {story['description']}")
                
                # 获取需求分类
                category_id = story.get('category_id')
                category_name = self.client.get_story_category(workspace_id, category_id)
                story['category_name'] = category_name

                # # 处理图片内容
                # image_links = self._extract_image_links(story['description'])
                # for img_url in image_links:
                #     img_download_url = self.get_img_download_url(story_url, img_url)
                #     if img_download_url:
                #         llm_content = self._call_llm_for_image(img_download_url)
                #         logger.info(f"图片内容解析结果: {llm_content}")
                #         # 将图片内容拼回描述
                #         story['description'] = story['description'].replace(img_url, f"{img_url}\n![图片内容]: {llm_content}\n")
                # logger.info(f"需求图片解析结果: {story['description']}")
                
                # 处理 iWiki 内容
                iwiki_url_id = self._match_iwiki_url_id(story_str)
                if iwiki_url_id:
                    iwiki_converter = IwikiToMdConverter()
                    story['description'] += iwiki_converter.convert(f"https://iwiki.woa.com/p/{iwiki_url_id}")
                    logger.info(f"iwiki 转换后的描述: {story['description']}")
                return story
        return None
    
    def get_img_download_url(self,require_url,img_url):
        match = self.match_url(require_url)
        # 处理产品需求
        if match:
            workspace_id, _ = match.groups()
            img_download_url = self.client.get_image(workspace_id,img_url)
            # logger.info(f"获取图片下载链接: {img_download_url}\n\n")
            return img_download_url
        else:
            logger.info(f"获取图片下载链接失败: {img_url}\n\n")
            return None


    
    def match_url(self, url):
        """
        匹配 TAPD URL 并提取 workspace_id 和 story_id
        :param url: TAPD 需求 URL
        :return: 匹配结果（re.Match 对象），如果 URL 无效则返回 None
        """
        patterns = [
            r"/tapd_fe/(\d+)/story/detail/(\d+)",
            r'https://tapd\.woa\.com/(\d+)/prong/stories/view/(\d+)',
            r"/tapd_fe/(\d+)/.*?dialog_preview_id=story_(\d+)"
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match
        return None

    def _call_llm_for_image(self, img_url):
        """
        调用 LLM 服务理解图片内容
        :param img_url: 图片下载链接
        :return: LLM 返回的图片内容描述
        """
        prompt="""
        对于图片，生成对图片概述性的描述
        """
        res = llm_vision(prompt,img_urls=[img_url],model="hunyuan-vision",is_stream=False,temperature=0.2,max_tokens=4096)

        return res

# exceptions.py
class TAPDError(Exception):
    """自定义API异常"""
    pass


class HTMLConversionError(Exception):
    """HTML转换异常"""
    pass


# config.py
from dataclasses import dataclass, field
from typing import List, Dict


@dataclass
class AppConfig:
    """应用配置"""
    auth_token: str = 'UHJvY2Vzc01hbmFnZVBsYXRmcm9tOjFFMjE2NEJCLUUzRkQtQTRBNi1FNUFDLUI1MDMxMDNDQzg3MQ=='
    excluded_tags: List[str] = field(default_factory=lambda: ['script', 'style', 'nav', 'footer'])
    markdown_config: Dict[str, bool] = field(default_factory=lambda: {'body_width': 0, 'wrap_links': True})
    api_timeout: int = 15
