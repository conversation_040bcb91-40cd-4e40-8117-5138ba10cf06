import os
from loguru import logger

from utils.logger.path import project_root_path

PROJECT_PATH = project_root_path('testgen')

# 全局标志，确保 logger.add() 只被调用一次
_logger_initialized = False


class Logging:

    def __init__(self, module_name=""):
        global _logger_initialized
        log_file_path = os.path.join(PROJECT_PATH, 'data/log')
        if module_name:
            log_file_path = os.path.join(log_file_path, module_name)
        # 判断目录是否存在，不存在则创建新的目录
        if not os.path.isdir(log_file_path):
            os.makedirs(log_file_path)

        log_file = os.path.join(log_file_path, '{time:YYYY-MM-DD}.log')
        
        # 确保 logger.add() 只被调用一次
        if not _logger_initialized:
            logger.add(
                log_file,  # 指定文件
                # 写入格式
                format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {elapsed} | {file} | {line} | {function} | {message}",
                encoding='utf-8',  # 编码格式
                rotation='1 days',  # 每天自动创建日志
                retention='10 days',  # 设置历史保留时长
                backtrace=True,  # 回溯
                diagnose=True,  # 诊断
                enqueue=True,  # 异步写入
                level='DEBUG'
            )
            _logger_initialized = True

    @staticmethod
    def get_logger():
        return logger


if __name__ == '__main__':
    pass
