import markdown
from xml.etree import ElementTree as ET
import xmind

def markdown_to_xmind(markdown_text, output_file, require_name="请输入产品需求"):
    """
    将 Markdown 文本转换为 XMind 思维导图文件。

    :param markdown_text: 输入的 Markdown 文本
    :param output_file: 输出的 XMind 文件路径
    """
    try:
        # 解析 Markdown 文本为 HTML
        html = markdown.markdown(markdown_text)
        root = ET.fromstring(f'<div>{html}</div>')
        # 创建 XMind 工作簿
        workbook = xmind.load(output_file)
        sheet = workbook.getPrimarySheet()
        sheet.setTitle('Markdown to XMind')
        root_topic = sheet.getRootTopic()
        root_topic.setTitle(require_name)

        # 用于存储不同层级的主题
        topic_stack = [root_topic]

        def process_element(element, parent_topic):
            if element.tag.startswith('h'):
                level = int(element.tag[1])
                title = element.text

                # 调整主题栈
                while len(topic_stack) > level:
                    topic_stack.pop()

                parent_topic = topic_stack[-1]
                new_topic = parent_topic.addSubTopic()
                new_topic.setTitle(title)
                topic_stack.append(new_topic)
                return new_topic
            # elif element.tag == 'ul':
            #     for li in element.findall('li'):
            #         list_item_topic = parent_topic.addSubTopic()
            #         list_item_topic.setTitle(li.text)
            #         sub_ul = li.find('ul')
            #         if sub_ul is not None:
            #             process_element(sub_ul, list_item_topic)
            #     return parent_topic
            # return parent_topic
            elif element.tag == 'ul':
                for li in element.findall('li'):
                    # 添加空节点作为父节点
                    empty_topic = parent_topic.addSubTopic()
                    empty_topic.setTitle("1、\n2、\n3、")
                    
                    # 将实际内容节点作为空节点的子节点
                    list_item_topic = empty_topic.addSubTopic()
                    list_item_topic.setTitle(li.text)
                    sub_ul = li.find('ul')
                    if sub_ul is not None:
                        process_element(sub_ul, list_item_topic)
                return parent_topic
            return parent_topic
        for element in root:
            process_element(element, topic_stack[-1])

        # 保存 XMind 文件
        xmind.save(workbook, output_file)
    except Exception as e:
        print(f"发生错误: {e}")


if __name__ == "__main__":
    markdown_text = """
# 一级标题 1
## 二级标题 1
1. 步骤
2. 步骤
3. 步骤
> 子列表项 1

# 一级标题 2
## 二级标题 2
- 列表项 2
"""
    output_file = 'output.xmind'
    markdown_to_xmind(markdown_text, output_file)
    print(f"XMind 文件已生成：{output_file}")
    