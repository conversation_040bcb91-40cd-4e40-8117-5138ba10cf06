import zipfile
import json
import xml.etree.ElementTree as ET

def parse_xmind_manually(xmind_file):
    try:
        # 解压 XMind 文件
        with zipfile.ZipFile(xmind_file, 'r') as z:
            content = z.read('content.json').decode('utf-8')  # 新版 XMind Zen
            data = json.loads(content)
            
            # 旧版 XMind（XML 格式）
            # content = z.read('content.xml').decode('utf-8')
            # root = ET.fromstring(content)
            
        # 解析 JSON 结构
        sheet = data[0]
        root_topic = sheet['rootTopic']
        requirement = root_topic.get('title', '未命名需求')
        cases = []
        current_dir = []

        def traverse(topic):
            title = topic.get('title', '').strip()
            children = topic.get('children', {}).get('attached', [])

            if title.startswith('*'):
                dir_name = title[1:].strip()
                current_dir.append(dir_name)
                for child in children:
                    traverse(child)
                current_dir.pop()

            elif '#' in title:
                case_title = title.split('#')[0].strip()
                level_part = title.split('#')[-1].strip()
                try:
                    level = int(level_part)
                except ValueError:
                    level = 0

                steps = []
                # for step_topic in children:
                #     step_text = step_topic.get('title', '').strip()
                #     expects = [child.get('title', '').strip() for child in step_topic.get('children', {}).get('attached', [])]
                #     if expects:
                #         step_text += "\n预期结果：" + "\n".join(expects)
                #     steps.append(step_text)
                for step_topic in children:
                    # step_text = step_topic.get('title', '').strip()
                    step_text = ""
                    expects = [child.get('title', '').strip() for child in step_topic.get('children', {}).get('attached', [])]
                    if expects:
                        step_text +=  "\n".join(expects)
                    steps.append(step_text)

                cases.append({
                    "目录": "/".join(current_dir),
                    "标题": case_title,
                    "预期": "\n".join(steps)
                })

            else:
                for child in children:
                    traverse(child)

        for child in root_topic.get('children', {}).get('attached', []):
            traverse(child)

        return {
            "需求名称": requirement,
            "测试用例": cases
        }

    except Exception as e:
        print(f"解析失败：{str(e)}")
        return {"需求名称": "", "测试用例": []}

# def generate_report(result):
#     report = []
#     # 添加需求名称
#     report.append(f"需求名称: {result['需求名称']}")
    
#     # 处理用例部分
#     if not result['测试用例']:
#         report.append("\n无可用测试用例")
#     else:
#         for idx, case in enumerate(result['测试用例'], 1):
#             # report.append(f"\n用例{idx}:")
#             report.append(f"用例标题：{case['目录']}/{case['标题']} ")
#             report.append("预期：" + case['预期'])
    
#     # 拼接最终字符串
#     return '\n'.join(report)

def generate_report(result):
    # 构建JSON格式的输出
    output = {
        "需求名称": result['需求名称'],
        "测试用例": []
    }
    
    # 处理用例部分
    if result['测试用例']:
        for case in result['测试用例']:
            output["测试用例"].append({
                "用例标题": f"{case['目录']}/{case['标题']}",
                "预期": case['预期']
            })
    
    return json.dumps(output, ensure_ascii=False, indent=2)

def xmind_parser(xmind_file):
    data = parse_xmind_manually(xmind_file)
    result = generate_report(data)
    print(result)
    return  result
