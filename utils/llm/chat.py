from openai import OpenAI
from openai import OpenAI
import os.path
from dotenv import load_dotenv
import os
from utils.logger.logger import Logging
import requests
import base64
# 加载模型环境
load_dotenv()
logger = Logging().get_logger()
hyvision_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_key'))
def hunyuan_vision(prompt,img_urls=[],model="hunyuan-vision",is_stream=False,temperature=0.2,max_tokens=8192):
    logger.info(f"hunyuan_vision_prompt: {prompt}")
    messages = []
    if len(img_urls) > 0:
        img_base64 = [url_to_base64(url) for url in img_urls]
        image_content =  [{"type": "image_url", "image_url":{"url": base64_str}} for base64_str in img_base64]
        add_mas="# 角色：你是一个测试用例编写大师，擅长结合图片（特别是图中有特殊标识的区域）和产品需求文档功能模块描述，生成测试用例。\n"
        prompt = add_mas + prompt
        messages.append(
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    *image_content
                ]
            })
    else:
        messages.append(
            {
                "role": "user",
                "content": prompt
            })
    response = hyvision_client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,  # 添加温度参数
        max_tokens=max_tokens,   # 添加最大令牌数
        stream=is_stream,
        response_format={"type": "json_object"} 
    )
    return response


def llm_vision(prompt,img_urls=[],model="hunyuan-vision",is_stream=False,temperature=0.2,max_tokens=8192):
    logger.info(f"hunyuan_vision_prompt: {prompt}")
    messages = []
    img_base64 = [url_to_base64(url) for url in img_urls]
    image_content =  [{"type": "image_url", "image_url":{"url": base64_str}} for base64_str in img_base64]
    messages.append(
        {
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                *image_content
            ]
        })
    response = hyvision_client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,  # 添加温度参数
        max_tokens=max_tokens,   # 添加最大令牌数
        stream=is_stream,
        response_format={"type": "json_object"} 
    )
    return response.choices[0].message.content

def chat_stream(prompt,model="hunyuan-turbo",is_stream=False,temperature=0.1,max_tokens=8192):
    response = hy_client.chat.completions.create(
        model=model,
        messages=[
            {"role": "user", "content": prompt }
        ],
        temperature=temperature,  # 添加温度参数
        max_tokens=max_tokens,   # 添加最大令牌数
        stream=is_stream,
        response_format={"type": "json_object"} 
    )
    return response

def url_to_base64(image_url):
    """
    将图片URL转换为base64编码字符串
    参数:
        image_url: 图片的URL地址
    返回:
        base64编码的图片字符串
    """
    try:
        # 下载图片
        response = requests.get(image_url)
        response.raise_for_status()
        
        # 获取图片内容类型
        content_type = response.headers.get('content-type', 'image/jpeg')
        
        # 转换为base64
        image_data = response.content
        base64_str = base64.b64encode(image_data).decode('utf-8')
        
        # 添加数据URI前缀
        if 'png' in content_type:
            prefix = 'data:image/png;base64,'
        elif 'jpeg' in content_type or 'jpg' in content_type:
            prefix = 'data:image/jpeg;base64,'
        else:
            prefix = 'data:image/png;base64,'  # 默认当作png处理
            
        return prefix + base64_str
        
    except requests.RequestException as e:
        logger.error(f"图片下载失败: {str(e)}")
        raise ValueError(f"无法下载或转换图片: {str(e)}")
