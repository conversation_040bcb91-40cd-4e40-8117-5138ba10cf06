import os

import requests
from requests import RequestException
from utils.logger.logger import Logging
logger = Logging().get_logger()

class DifyClient:
    def __init__(self, base_url='http://idify.woa.com/v1'):
        self.base_url = base_url
        auth_token = os.getenv("DIFY_PROD_TOKEN")

        self.headers = {
            'Authorization': f'Bearer {auth_token}' if auth_token else ''
        }



    def query_dify(self, query: str, namespace: str, collectionCodes: str):
        """
        :param collectionCodes: trag分片，字符串格式，请求多个分片按照 "a,b,c"形式拼接
        :param namespace: trag工作空间，字符串格式
        :param query:提问
        :return: dify工作流回答
        description:
            使用阻塞模式请求dify工作流，响应时间较长，可能存在超时自动断开情况
        """

        body = {
                    "inputs": {
                        "need_query_parse": "false",
                        "namespace": namespace,
                        "collectionCodes": collectionCodes,
                        "embedding_model": "public-bge-m3"
                    },
                    "query": query,
                    "response_mode": "blocking",
                    "conversation_id": "",
                    "user": "yangpengguo",
                }
        logger.info(f"请求Dify工作流: {body}")
        try:
            response = requests.post(
                f"{self.base_url}/chat-messages",
                headers=self.headers,
                json=body
            )
            response.raise_for_status()
            dify_data = response.json()

            return dify_data.get('answer')
        except RequestException as e:
            raise ValueError(f"无法请求工作流: {str(e)}") from e
