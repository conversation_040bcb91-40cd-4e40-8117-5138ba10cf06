"""
Bug召回功能配置文件
"""

# Bug召回相关配置
BUG_RECALL_CONFIG = {
    # 输出文件配置
    "output": {
        "base_dir": "data/bug_recall",
        "file_format": "xlsx",
        "date_format": "%Y%m%d",
        "timestamp_format": "%Y%m%d_%H%M%S"
    },
    
    # TRAG向量搜索配置
    "trag": {
        "bug_namespace": None,  # 从环境变量BUG_NAMESPACE获取
        "bug_collection": None,  # 从环境变量BUG_COLLECTION获取
        "search_limit": 50,
        "enable_rerank": True,
        "rerank_model": "bge-reranker-v2-m3",
        "embedding_model": "public-bge-m3"
    },

    # Bug分析配置
    "analysis": {
        "max_bugs_per_story": 50,  # 每个需求最多分析的Bug数量
        "max_relevant_bugs": 20,   # 最多保留的相关Bug数量
        "min_relevance_score": 6.0,  # 最低相关性评分阈值
        "max_test_cases": 15,      # 最多生成的测试用例数量
        "trag_score_weight": 0.4,  # TRAG评分权重
        "llm_score_weight": 0.6    # LLM评分权重
    },
    
    # LLM调用配置
    "llm": {
        "max_tokens": 8096,
        "temperature": 0.3,
        "timeout": 60  # 超时时间（秒）
    },
    
    # TAPD API配置
    "tapd": {
        "max_retries": 3,
        "retry_delay": 1,  # 重试间隔（秒）
        "timeout": 30      # API超时时间（秒）
    },
    
    # Excel输出配置
    "excel": {
        "sheets": {
            "story_info": "需求信息",
            "bug_modules": "Bug风险模块",
            "relevant_bugs": "相关Bug",
            "test_cases": "建议测试用例"
        },
        "column_width": 20,
        "text_wrap": True
    },
    
    # Bug相关性评分标准
    "relevance_criteria": {
        "high": 8.0,      # 高相关性阈值
        "medium": 6.0,    # 中等相关性阈值
        "low": 4.0        # 低相关性阈值
    },
    
    # 测试用例优先级配置
    "test_case_priority": {
        "critical": "P0",
        "high": "P1", 
        "medium": "P2",
        "low": "P3"
    },
    
    # Bug风险等级配置
    "risk_levels": {
        "critical": "严重",
        "high": "高",
        "medium": "中",
        "low": "低"
    }
}

# Bug类型映射
BUG_TYPE_MAPPING = {
    "功能缺陷": "functional",
    "性能问题": "performance", 
    "界面问题": "ui",
    "兼容性问题": "compatibility",
    "安全问题": "security",
    "数据问题": "data",
    "其他": "other"
}

# 测试用例类型映射
TEST_CASE_TYPE_MAPPING = {
    "功能测试": "functional",
    "性能测试": "performance",
    "界面测试": "ui", 
    "兼容性测试": "compatibility",
    "安全测试": "security",
    "数据测试": "data",
    "回归测试": "regression",
    "边界测试": "boundary",
    "异常测试": "exception"
}

# 默认Prompt模板配置
PROMPT_TEMPLATES = {
    "bug_modules": {
        "system": "你是一个专业的软件测试专家，擅长分析需求文档并识别潜在的Bug风险点。",
        "max_modules": 8,
        "output_format": "json"
    },
    
    "bug_relevance": {
        "system": "你是一个专业的Bug分析专家，擅长评估Bug与需求的相关性。",
        "score_range": "0-10",
        "output_format": "json"
    },
    
    "test_cases": {
        "system": "你是一个专业的测试用例设计专家，擅长基于Bug历史设计针对性的测试用例。",
        "max_cases": 15,
        "output_format": "json"
    }
}

def get_config(key=None):
    """
    获取配置项
    :param key: 配置键，如果为None则返回全部配置
    :return: 配置值
    """
    if key is None:
        return BUG_RECALL_CONFIG
    
    keys = key.split('.')
    config = BUG_RECALL_CONFIG
    
    for k in keys:
        if k in config:
            config = config[k]
        else:
            return None
    
    return config

def update_config(key, value):
    """
    更新配置项
    :param key: 配置键（支持点分隔的嵌套键）
    :param value: 配置值
    """
    keys = key.split('.')
    config = BUG_RECALL_CONFIG
    
    for k in keys[:-1]:
        if k not in config:
            config[k] = {}
        config = config[k]
    
    config[keys[-1]] = value

# 验证配置的有效性
def validate_config():
    """
    验证配置的有效性
    :return: 验证结果和错误信息
    """
    errors = []
    
    # 检查必要的配置项
    required_keys = [
        'output.base_dir',
        'analysis.max_bugs_per_story',
        'llm.max_tokens',
        'tapd.max_retries'
    ]
    
    for key in required_keys:
        if get_config(key) is None:
            errors.append(f"缺少必要配置项: {key}")
    
    # 检查数值配置的合理性
    if get_config('analysis.max_bugs_per_story') <= 0:
        errors.append("analysis.max_bugs_per_story 必须大于0")
    
    if get_config('analysis.min_relevance_score') < 0 or get_config('analysis.min_relevance_score') > 10:
        errors.append("analysis.min_relevance_score 必须在0-10之间")
    
    if get_config('llm.max_tokens') <= 0:
        errors.append("llm.max_tokens 必须大于0")
    
    return len(errors) == 0, errors

if __name__ == "__main__":
    # 测试配置
    print("Bug召回配置测试")
    print("=" * 30)
    
    # 验证配置
    is_valid, errors = validate_config()
    if is_valid:
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    
    # 显示主要配置
    print(f"\n输出目录: {get_config('output.base_dir')}")
    print(f"最大Bug数量: {get_config('analysis.max_bugs_per_story')}")
    print(f"最低相关性评分: {get_config('analysis.min_relevance_score')}")
    print(f"LLM最大Token数: {get_config('llm.max_tokens')}")
    
    # 测试配置更新
    print(f"\n更新前的超时时间: {get_config('llm.timeout')}")
    update_config('llm.timeout', 120)
    print(f"更新后的超时时间: {get_config('llm.timeout')}")
