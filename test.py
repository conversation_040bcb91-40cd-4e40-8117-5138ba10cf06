# from rag_server.search import search_document
# import os
# from dotenv import load_dotenv
# load_dotenv()

# import requests

# url = "http://testgen.devcloud.woa.com:5001/medical_case_recall_evalution"
# headers = {"Content-Type": "application/json"}
# data = {
#     "story_url": "https://tapd.woa.com/tapd_fe/20452645/story/detail/1020452645124793897"
# }

# response = requests.post(url, headers=headers, json=data)
# print(response.text)

# from utils.iwiki import IwikiManager

# if __name__ == '__main__':
#     iwiki = IwikiManager()
#     # dirs = iwiki.get_all_directories("TESP")
#     # print(dirs)
#     parent_id = "**********"
#     res = iwiki.fetch_children(parent_id)
#     print(res)

from story import StoryProcessor
from utils.logger.logger import Logging
from utils.tapd import TAPDUtils

logger = Logging().get_logger()
if __name__ == '__main__':
    origin_story = TAPDUtils().get_story("https://tapd.woa.com/tapd_fe/20452645/story/detail/1020452645121379275")
    
    story_processor = StoryProcessor(origin_story)
    logger.info(f"处理后的需求文档: {story_processor.story}")
    logger.info(f"提取的核心功能模块: {story_processor.story_modules}")
    
    # 使用 RAG 补充需求理解
    supplemented_story = story_processor.rag_for_story()
    logger.info(f"补充后的需求文档: {supplemented_story}")
    
