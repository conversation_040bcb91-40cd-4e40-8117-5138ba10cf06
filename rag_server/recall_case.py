import time

from openai import OpenAI
import json
import os.path
from dotenv import load_dotenv
import os
from tools.recall_case_tool import request_dify_recall_case
from tools.regress import to_excel, extract_module_from_story, find_modules,polish_story
from utils.logger.logger import Logging
from utils.dify import DifyClient
from utils.tapd import TAPDUtils

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()


class ReCallCase():
    namespace = ""
    collection_codes = ""

    def __init__(self, story_url):
        self.story_url = story_url

        self.tapd_utils = TAPDUtils()

        # 初始化解析tapd数据，获取核心功能点
        self.cores, self.story = self.parse_tapd_case()

        self.case_list = {"result": [], "rag_doc": [], "rerank_doc": []}

        # 初始化Dify客户端
        self.dify_client = DifyClient()
        self.workspace_id, self.story_id = self.tapd_utils.match_url(self.story_url).groups()

        if self.workspace_id == "20452645":
            logger.info(f"正在检索SAAS知识库用例集")
            self.knowledge_base = "SAAS用例"
            self.namespace = os.getenv("SAAS_NAMESPACE")
            self.collection_codes = os.getenv("SAAS_MODULE_CASE_ALL_COLL")
        else:
            self.workspace_id = "20375472"
            logger.info(f"正在检索健康知识库用例集")
            self.knowledge_base = "健康 checklist 用例"
            self.namespace = os.getenv("HEALTH_NAMESPACE")
            self.collection_codes = os.getenv("HEALTH_MODULE_CHECKLIST_CASE_COLL")

    def parse_tapd_case(self):
        """
        解析tapd需求，生成核心测试点
        """
        story = self.tapd_utils.get_story(self.story_url)
        # 润色 story
        story['description'] = polish_story(story['description'])
        # 解析需求核心功能模块
        cores = extract_module_from_story(story)
        
        return cores, story

    def gen_regress_case(self, item, is_zhiyan=False, module_id=""):
        result, rag_doc, rerank_doc, query = request_dify_recall_case(
            item,
            self.story,
            self.knowledge_base,
            self.dify_client,
            self.namespace,
            self.collection_codes
        )
        res_dict = {"result": [], "rag_doc": [], "rerank_doc": [], "query": query}
    
        # 分别对result、rag_doc、rerank_doc去重
        for cases, key in zip([result, rag_doc, rerank_doc], ["result", "rag_doc", "rerank_doc"]):
            seen = []
            for case in cases:
                if case is not None:
                    key_tuple = (case.get("用例目录", ""), case.get("用例标题", ""))
                    key_str = json.dumps(key_tuple, ensure_ascii=False, sort_keys=True)
                
                    # 如果不在历史case中，则添加到seen和case_list
                    if key_str not in self.case_list[key]:
                        self.case_list[key].append(key_str)
                    
                        if is_zhiyan:
                            seen.append({
                                "id": str(time.time()),
                                "content": [
                                    {
                                        "Name": case['用例标题'],
                                        "PreConditions": "",
                                        "Priority": case['用例等级'],
                                        "Steps": [
                                            {
                                                "Content": case['步骤描述'],
                                                "ExpectedResult": case['预期结果']
                                            }
                                        ],
                                        "Kind": "CASE",
                                        "Source": "MANUALLY"
                                    }
                                ],
                                "parent_id": module_id,
                                "node_type": "CASE"
                            })
                        else:
                            seen.append(case)
            res_dict[key] = seen  # 将去重后的数据存储到对应的键中
    
        return res_dict
