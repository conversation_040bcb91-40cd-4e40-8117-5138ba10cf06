# TRAG Manager 模块文档

`trag_manager.py` 是一个用于管理 TRAG（测试用例知识库）的工具模块，支持测试用例的导入、搜索、清理以及需求文档的管理。
trag 官方文档：https://iwiki.woa.com/space/trag

## 安装依赖
```python
pip install langchain==0.2.16 langchain-core==0.2.38
pip install venus-sdk==1.3.26 trag trag-langchain trag-client -i  https://mirrors.tencent.com/pypi/simple  --extra-index-url https://mirrors.tencent.com/pypi/simple/
```


## 功能概述

1. **测试用例管理**：支持从 Excel 文件导入测试用例到 TRAG 知识库。
2. **需求文档管理**：支持从 TAPD 需求链接导入需求文档到 TRAG 知识库。
3. **搜索功能**：支持根据查询字符串搜索测试用例或需求文档。
4. **清理功能**：支持清空 TRAG 知识库中的文档。
5. **目录管理**：支持从用例文件中提取二级目录并导入到知识库。

### `TRAGManager` 类

**功能**：管理 TRAG 文档的导入、搜索和清理。
# 注意：
1. 创建知识库前需要创建命名空间，命名空间和知识库为 1 对多关系。
2. 创建知识库选择创建带过滤标签的知识库create_field_coll，才能起到过滤检索目的。
3. 在创建完知识库后，打印知识库信息，拿到 col_code 后，初始化 TRAGManager,便可执行导入和删除操作。
4. 若要更新知识库，建议把原来知识库内容删除manager.clean_documents()，重新导入。
5. 当前方案涉及到目录知识库和用例知识库、更新时需要同步更新。


#### 主要方法
- `import_test_cases(file_path)`：从 Excel 文件导入测试用例到 TRAG。
- `import_require(urls)`：从 TAPD 需求链接列表导入需求文档到 TRAG。
- `search_test_cases(query, limit, filter_expr)`：搜索测试用例。
- `clean_documents()`：清空 TRAG 知识库中的文档。
- `import_case_directories(file_path)`：从 Excel 文件导入用例目录到知识库。

## 使用示例

### 初始化 TRAGManager
```python
manager = TRAGManager(ns_code="your_namespace", coll_code="your_collection")
```

### 创建命名空间和知识库
```python
# 创建命名空间
rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
ns = rag.create_namespace("医典", description="")
print(ns.info)

# 创建带过滤标签的知识库
coll = create_field_coll(ns, "story", "存放 SAAS 业务需求文档", embedding_model="public-bge-m3", dimension=1024)
print(coll.info)
```

### 导入测试用例
```python
manager.import_test_cases("path/to/test_cases.xlsx")
```

### 导入需求文档
```python
urls = ["https://tapd.woa.com/tapd_fe/123456/story/detail/123456789"]
manager.import_require(urls)
```

### 搜索测试用例
```python
results = manager.search_test_cases(query="健康管理助手", limit=10)
for result in results:
    print(result)
```

### 清空知识库
```python
manager.clean_documents()
```

## 注意事项
1. **环境变量**：确保以下环境变量已配置：
   - `TRAG_TOKEN`：TRAG API 密钥。
   - `SASS_NAMESPACE`：命名空间代码。
   - `SASS_CASE_COLL`：知识库集合代码。
2. **文件格式**：导入的 Excel 文件需包含指定的表头字段。
3. **TAPD 链接**：确保提供的 TAPD 链接格式正确。

## 依赖
- `openpyxl`：用于处理 Excel 文件。
- `python-dotenv`：用于加载环境变量。
- `trag`：TRAG Python SDK。

---
