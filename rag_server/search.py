import requests
import os
from dotenv import load_dotenv
from typing import List

from trag import TRAG
from trag.types import Document
load_dotenv()

def search(
    rag_code=os.getenv("RAG_CODE"),
    ns_code=None,
    coll_code=None,
    docs=None,
    limit=5,
    filter_expr="",
    embedding_model="public-bge-m3"
):
    """
    执行搜索请求，从指定的集合中检索与输入文档相关的条目。
 
    Args:
        rag_code (str): RAG 应用code，默认从环境变量中读取。
        ns_code (str): 命名空间code。
        coll_code (list): 知识库 code列表。
        docs (list): 输入文档列表，用于检索相关内容。
        limit (int): 返回结果的最大数量，默认为5。
        filter_expr (str): 过滤表达式，用于筛选结果。
 
    Returns:
        requests.Response: 包含搜索结果的响应对象。
    """
    # 检查 rag_code 是否为 None
    if rag_code is None:
        raise ValueError("RAG_CODE 环境变量未设置，请检查 .env 文件或环境变量配置。")

    # 初始化默认值
    if docs is None:
        docs = [""]
    if coll_code is None:
        coll_code = [""]
    # 构造请求URL和头部
    url = "http://api.trag.woa.com/v1/application/search/collection"
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {os.getenv('TRAG_TOKEN')}",
    }
    # 构造请求数据
    data = {
        "ragCode": rag_code,
        "namespaceCode": ns_code,
        "collectionCodes": coll_code,
        "embeddingModel": embedding_model,
        "filterExpr": filter_expr,
        "docs": docs,
        "limit": limit,
    }
    response = requests.post(url, headers=headers, json=data)
    return response

def rerank(
    rag_code=os.getenv("RAG_CODE"),
    namespace_code=None,
    model="bge-reranker-v2-m3",
    query="",
    documents=None,
):
    """
    执行重排序请求，对输入的文档列表进行相关性排序。

    Args:
        rag_code (str): RAG 应用code，默认从环境变量中读取。
        namespace_code (str): 命名空间code。
        model (str): 使用的重排序模型，默认为"bge-reranker-v2-m3"。
        query (str): 查询文本。
        documents (list): 需要排序的文档列表。

    Returns:
        requests.Response: 包含重排序结果的响应对象。
    """
    if documents is None:
        documents = []

    url = "http://api.trag.woa.com/v1/trag/retrieval/rerank"
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {os.getenv('TRAG_TOKEN')}",
    }

    data = {
        "ragCode": rag_code,
        "namespaceCode": namespace_code,
        "model": model,
        "query": query,
        "documents": documents,
    }

    response = requests.post(url, headers=headers, json=data)
    return response


def search_document(
    rag_code=os.getenv("RAG_CODE"),
    namespace_code=None,
    collection_code=None,
    collection_alias=None,
    vector=None,
    doc="",
    embedding_model="public-bge-m3",
    retrieve_vector=False,
    limit=5,
    filter_expr="",
    sharding_key=None,
    enable_sentence=False,
    enable_rerank=True,
    re_rank_model="bge-reranker-v2-m3",
    with_query_instruction=False,
    skip_sharding_key_filter=False,
    embedding_with_cache=False,
):
    """
    执行搜索和重排序请求，结合搜索和重排序功能。

    Args:
        rag_code (str): RAG 应用code，默认从环境变量中读取。
        namespace_code (str): 命名空间code。
        collection_code (str): 知识库code。
        collection_alias (str): 知识库别名。
        vector (str): 向量值。
        doc (str): 输入文档文本。
        embedding_model (str): 使用的嵌入模型，默认为"public-bge-m3"。
        retrieve_vector (bool): 是否返回向量值，默认为False。
        limit (int): 返回结果的最大数量，默认为5。
        filter_expr (str): 过滤表达式，用于筛选结果。
        sharding_key (str): 分片键。
        enable_sentence (bool): 是否启用句子级别结果，默认为False。
        enable_rerank (bool): 是否启用重排序，默认为True。
        re_rank_model (str): 使用的重排序模型，默认为"bge-reranker-v2-m3"。
        with_query_instruction (bool): 是否包含查询指令，默认为False。
        skip_sharding_key_filter (bool): 是否跳过分片键过滤，默认为False。
        embedding_with_cache (bool): 是否使用缓存嵌入，默认为False。

    Returns:
        requests.Response: 包含搜索和重排序结果的响应对象。
    """
    url = "http://api.trag.woa.com/v1/trag/collection/document/search"
    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {os.getenv('TRAG_TOKEN')}",
    }

    data = {
        "ragCode": rag_code,
        "namespaceCode": namespace_code,
        "collectionCode": collection_code,
        "collectionAlias": collection_alias,
        "vector": vector,
        "doc": doc,
        "embeddingModel": embedding_model,
        "retrieveVector": retrieve_vector,
        "limit": limit,
        "filterExpr": filter_expr,
        "shardingKey": sharding_key,
        "enableSentence": enable_sentence,
        "enableRerank": enable_rerank,
        "reRankModel": re_rank_model,
        "withQueryInstruction": with_query_instruction,
        "skipShardingKeyFilter": skip_sharding_key_filter,
        "embeddingWithCache": embedding_with_cache,
    }

    response = requests.post(url, headers=headers, json=data)
    return response



if __name__ == "__main__":
    # 示例1：医典检索
    # response = search(
    #     rag_code=os.getenv("RAG_CODE"),
    #     ns_code=os.getenv("MD_NAMESPACE"),
    #     coll_code=[os.getenv("MD_CASE_COLL")],
    #     docs=["内容反馈"],
    #     limit=5,
    # )
    # print(response.json())

    # # 示例2：医典带过滤表达式检索
    # filter_expr = 'module in ("药品类词条")'
    # response = search(
    #     rag_code=os.getenv("RAG_CODE"),
    #     ns_code=os.getenv("MD_NAMESPACE"),
    #     coll_code=[os.getenv("MD_CASE_COLL")],
    #     docs=["药品"],
    #     limit=5,
    #     filter_expr=filter_expr,
    # )
    # print(response.json())

    # # 示例2：健康检索
    # response = search(
    #     rag_code=os.getenv("RAG_CODE"),
    #     ns_code=os.getenv("HEALTH_NAMESPACE"),
    #     coll_code=[os.getenv("HEALTH_MODULE_CHECKLIST_CASE_COLL")],
    #     docs=["健康问问"],
    #     limit=5
    # )
    # print(response.json())

    # 示例3：健康带过滤表达式检索
    filter_expr = 'module in ("饮食助手")'
    response = search(
        rag_code=os.getenv("RAG_CODE"),
        ns_code=os.getenv("HEALTH_NAMESPACE"),
        coll_code=[os.getenv("HEALTH_MODULE_CHECKLIST_CASE_COLL")],
        docs=["饮食"],
        limit=5,
        filter_expr=filter_expr,
    )
    print(response.json())
    #
    # ns_code = os.getenv("SASS_NAMESPACE")  # 目标Namespace
    # col_code = os.getenv("SAAS_STORY_COLL")  # 目标Collection
    #
    # trag_token = os.getenv("TRAG_TOKEN")
    # rag = TRAG.from_api_key(api_key=trag_token)
    # ns = rag.namespace(ns_code)
    # coll = ns.collection(col_code)
    # search_result = coll.search_documents(
    #     doc="医生详情", embedding_model="bge-large-zh"
    # )
    # print(search_result)
