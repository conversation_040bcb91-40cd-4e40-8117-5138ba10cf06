import pandas as pd # type: ignore
import gradio as gr # type: ignore
import pandas as pd # type: ignore
from pandas import DataFrame # type: ignore
from openai import OpenAI # type: ignore
import json
import os.path
from dotenv import load_dotenv # type: ignore
from io import StringIO
from datetime import datetime
import os
from utils.logger.logger import Logging
from utils.llm.chat import chat_stream
from utils.llm.prompt import polish_story_prompt,gen_function_modules_prompt,filter_relevant_modules_prompt,rag_for_story_prompt,find_modules_prompt,extract_module_from_story_prompt,polish_function_modules_prompt
from utils.dify import DifyClient
from utils.tapd import TAPDUtils
from utils.iwiki import IwikiToMdConverter
from rag_server.search import search_document,search,rerank

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()

class StoryProcessor:
    def __init__(self, origin_story):
        self.origin_story = origin_story
        self.story = self._polish_story()
        self.story_modules = self._extract_module_from_story()

    def _extract_module_from_story(self):
        """
        从需求文档中提取模块
        :param story: 需求文档
        :return: 模块列表
        """
        # 提取核心功能模块
        prompt = gen_function_modules_prompt(self.story)
        modules = chat_stream(prompt, max_tokens=8096).choices[0].message.content
        logger.info(f"从需求分割提取的核心功能模块列表:\n {modules}")
        
        # 润色核心功能模块
        # modules = chat_stream(polish_function_modules_prompt(self.story['description'],modules)).choices[0].message.content
        # logger.info(f"润色后的核心功能模块列表: {modules}")

        # 使用大模型筛选提取后的核心功能模块
        modules = chat_stream(filter_relevant_modules_prompt(self.story,modules)).choices[0].message.content
        logger.info(f"筛选后的核心功能模块列表: {modules}")  
        modules = json.loads(modules)
        
        # 如果核心功能模块数量超过8个，则保留前8个
        modules = modules[:8] if len(modules) > 8 else modules
        logger.info(f"最终核心功能模块列表: {modules}")
        return modules

    def _polish_story(self):
        """
        对需求文档进行润色
        :param story: 需求文档
        :return: 润色后的需求文档
        """
        prompt = polish_story_prompt(self.origin_story)
        polished_story = chat_stream(prompt, max_tokens=16192).choices[0].message.content
        logger.info(f"润色后的需求文档: {polished_story}")
        return polished_story
    
    def rag_for_story(self):
        """
        使用 rag 召回历史相关需求和业务文档，补充需求理解,返回补充后的需求文档
        """
        res = search(
            ns_code=os.getenv("SAAS_NAMESPACE"),
            coll_code=[os.getenv("SAAS_STORY_COLL")],
            docs=[self.story],
            limit=10,
        )

        data = res.json()
        logger.info(f"召回的文档: {data}")
        docs = [
            doc["doc"]
            for docs_group in data["data"]["documents"].values()
            for doc in docs_group[0]
        ]
        logger.info(f"粗召回的文档数量: {docs}")
        rerank_data = rerank(
            rag_code=os.getenv("RAG_CODE"),
            namespace_code=os.getenv("SAAS_NAMESPACE"),
            documents=docs,
            query=self.story,
            model="bge-reranker-v2-m3"
        )
        rerank_data = rerank_data.json()
        # 取重排序后的前2个index
        rerank_index = [item["index"] for item in rerank_data["data"][:2]]
        # 按 index 取出对应文档
        rerank_modules = [docs[i] for i in rerank_index]
        logger.info(f"重排序后的模块目录: {rerank_modules}")
        
        # 将重排序后的文档用于补充需求文档描述
        res = chat_stream(rag_for_story_prompt(self.story, rerank_modules), 
            max_tokens=16192
        ).choices[0].message.content
        
        return res
