FROM mirrors.tencent.com/tencentos/tencentos4-microdnf AS build

RUN microdnf install -y python3-virtualenv && python3 -m venv /venv

FROM build AS build-venv
COPY requirements.txt /requirements.txt
RUN /venv/bin/pip install --disable-pip-version-check -r /requirements.txt
RUN /venv/bin/pip install -U venus-sdk==1.3.26 trag==0.0.15 trag-langchain==0.0.31 trag-client==0.6.0 -i https://mirrors.tencent.com/pypi/simple --extra-index-url https://mirrors.tencent.com/pypi/simple/


FROM mirrors.tencent.com/tencentos/tencentos4-rt-python3.11
COPY --from=build-venv /venv /venv
COPY . /app
WORKDIR /app

EXPOSE 80
EXPOSE 8000
EXPOSE 5000
ENTRYPOINT ["/venv/bin/python3", "api_sse_service.py"]