import re

from flask import jsonify
from openai import OpenAI
import json
from dotenv import load_dotenv
import os
from tools.regress import to_excel, extract_module_from_story, find_modules,find_modules_for_saas,polish_story
from utils.logger.logger import Logging
import time

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()


def request_dify_recall_case(core_item, story, knowledge_base, dify_client, namespace, collection_codes):
    """
    请求dify工作流，召回相似度高的回归用例，一个query为维度
    @core_item 分析出来的功能点
    @story 需求信息
    @knowledge_base 业务信息
    @dify_client dify实例
    @namespace rag工作空间
    @collection_codes rag知识库分片
    """
    core = {}
    core['name'] = core_item['name']
    core['description'] = core_item['description']
    core['关联需求'] = story['name']
    core['模块'] = story['category_name']
    core['story'] = json.dumps(story, ensure_ascii=False)
    start_time = time.time()
    # 根据核心功能描述查询最相关的模块目录
    if namespace == os.getenv("SAAS_NAMESPACE"):
        modules = find_modules_for_saas(json.dumps(core, ensure_ascii=False))
    elif namespace == os.getenv("HEALTH_NAMESPACE"):
        modules = find_modules(core, knowledge_base)

    logger.info(f"相关模块目录: {modules}\n")
    core['模块'] = modules
    query = []
    core = json.dumps(core, ensure_ascii=False)
    logger.info(f"当前检索 query: {core}")
    query.append(core)
    query = json.dumps(query, ensure_ascii=False)
    try:
        # 调用Dify API获取用例数据
        res = dify_client.query_dify(query, namespace, collection_codes)
        res = json.loads(res)
        result = res.get("result", [])
        rag_doc =  res.get("rag_doc", [])
        rerank_doc =  res.get("rerank_doc", [])
        end_time = time.time()

        logger.info(f"关于{query}的用例如下:\n {result}\n 耗时: {end_time - start_time:.2f}秒")
    except Exception as e:
        logger.error(f"请求Dify API失败: {str(e)}")
        logger.error(f"失败的核心功能项: {query}")

    return result, rag_doc, rerank_doc, [core]


def create_response(code, msg, data=None):
    logger.info(f"返回响应: {code}, {msg}, {data}")
    return jsonify({
        "code": code,
        "msg": msg,
        "data": data or {}
    })


def extract_https_links(text):
    # 正则表达式匹配以https://开头的URL
    pattern = r'https://[^\s"]+'
    https_links = re.findall(pattern, text)
    return https_links
