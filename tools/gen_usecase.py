import pandas as pd
import gradio as gr
import re
import ast
from openai import OpenAI
import os.path
from dotenv import load_dotenv
from io import StringIO
from datetime import datetime
import os
import json
from utils.logger.logger import Logging
from utils.llm.chat import chat_stream, hunyuan_vision
from utils.llm.prompt import extract_document_title, format_require_prompt, is_need_gen_case, gen_case_prompt,case_level_prompt
from utils.tapd import TAPDUtils
from utils.xmind.xmind_parser import xmind_parser
from utils.xmind.gen_xmind import markdown_to_xmind
# 加载模型环境
load_dotenv()
logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_key'))

def gen_case_path(require_name):
    case_dir = "data/cases"
    # 确保输出目录存在
    os.makedirs(case_dir, exist_ok=True)
    
    # 替换文件名中的特殊字符
    safe_name = re.sub(r'[\\/:*?"<>|]', '_', require_name)  # 替换所有路径非法字符为下划线
    
    # 生成唯一的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    case_path = os.path.join(case_dir, f"{safe_name}_{timestamp}.xmind")
    
    # 确保父目录存在
    os.makedirs(os.path.dirname(case_path), exist_ok=True)
    
    if not os.path.exists(case_path):
        # 如果文件不存在，创建一个新文件
        with open(case_path, "w") as file:
            print(f"File '{case_path}' created.")
    else:
        print(f"File '{case_path}' already exists.")
    return case_path



def csv_string_to_excel(output_path, csv_string):
    """
    将 CSV 格式的字符串转换为 Excel 文件并保存
    :param csv_string: CSV 格式的字符串
    :param output_dir: 输出目录，默认为 "output"
    :return: 生成的 Excel 文件路径
    """
    print("正在转换 CSV 字符串为 Excel 文件...", csv_string)
    # 将 CSV 字符串转换为 DataFrame
    try:
        # 使用 pandas 内置方法解析 CSV 字符串
        csv_file = StringIO(csv_string)
        df = pd.read_csv(csv_file)
        
        # 新增“是否采纳”列，默认值为空字符串
        df['是否采纳'] = ''
        
        # 使用 ExcelWriter 的覆盖模式保存
        with pd.ExcelWriter(output_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
            # 获取工作表对象
            worksheet = writer.sheets['Sheet1']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 15  # 第一列宽度
            worksheet.column_dimensions['B'].width = 80  # 第二列宽度
            worksheet.column_dimensions['C'].width = 10  # 第三列宽度
            # worksheet.column_dimensions['D'].width = 60  # 第四列宽度
            # worksheet.column_dimensions['E'].width = 10  # 第四列宽度
            # worksheet.column_dimensions['F'].width = 20  # 第四列宽度

        return output_path
    except Exception as e:
        print(f"转换失败: {e}")
        return None


import csv
from io import StringIO

def csv_to_markdown(csv_string):
    """
    将 CSV 字符串转换为 Markdown 表格格式。

    :param csv_string: 包含 CSV 数据的字符串
    :return: Markdown 格式的表格字符串
    """
    # 使用 StringIO 将字符串转换为文件对象
    f = StringIO(csv_string)
    reader = csv.reader(f)
    
    # 读取所有行
    rows = list(reader)
    
    if not rows:
        print("CSV 字符串为空，无法转换为 Markdown 表格。")
        return ""
    
    # 构建表头
    header = rows[0]
    markdown_table = "| " + " | ".join(header) + " |\n"
    markdown_table += "| " + " | ".join(["---"] * len(header)) + " |\n"
    
    # 构建表格内容
    for row in rows[1:]:
        markdown_table += "| " + " | ".join(row) + " |\n"
    
    return markdown_table
    
def get_file_type(file_path):
    """
    判断文件类型是excel还是xmind
    """
    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension in ['.xlsx', '.xls']:
        return 'excel'
    elif file_extension == '.xmind':
        return 'xmind'
    else:
        raise ValueError(f"不支持的文件类型: {file_extension}")

def split_document_by_titles(document, titles):
    result = []
    current_title = None
    current_content = []
    used_titles = set()
    lines = document.splitlines()

    for line in lines:
        for title in titles:
            if title in line.strip() and title not in used_titles:
                if current_title is not None:
                    result.append({
                        "title": current_title,
                        "content": '\n'.join(current_content).strip()
                    })
                    current_content = []
                current_title = line.strip()
                used_titles.add(title)
                break
        else:
            if current_title is not None:
                current_content.append(line)

    if current_title is not None:
        result.append({
            "title": current_title,
            "content": '\n'.join(current_content).strip()
        })

    return result

def get_rquire(url):
    tapd_utils = TAPDUtils()
    description = tapd_utils.get_story_description(url)
    require_name = tapd_utils.get_story_name(url)
    return description, require_name

def get_img_download_url(require_url,img_url):
    tapd_utils = TAPDUtils()
    img_url = tapd_utils.get_img_download_url(require_url,img_url)
    return img_url
    
def clean_markdown_labels(markdown_text):
    """
    去除Markdown文本中的特定字段，包括：
    - "用例标题："、"预期结果："、"目录标题："、"子目录标题："
    - "用例"开头+数字+"："格式的字段（如"用例1："、"用例2："等）
    
    :param markdown_text: 原始Markdown文本
    :return: 处理后的Markdown文本，保留其他所有内容
    """
    # 定义需要删除的固定字段列表
    labels_to_remove = ["用例标题：", "预期结果：", "子目录标题：", "目录标题："]
    
    # 按行处理文本
    lines = markdown_text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 删除所有固定的字段
        for label in labels_to_remove:
            line = line.replace(label, "")
        
        # 使用正则表达式删除"用例"+数字+"："格式的字段
        import re
        line = re.sub(r'用例\d+：', '', line)
        
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def get_markdown_usecase(markdown_text):
    # 先过滤掉空行和只有空格的行
    lines = [line for line in markdown_text.split('\n') if line.strip()]
    result = []
    for i in range(len(lines) - 1):
        if lines[i].startswith('#') and not lines[i + 1].startswith('#'):
            result.append(lines[i])
    return result



def update_markdown_usecase(markdown_text, usecase):
    # 先过滤掉空行和只有空格的行
    lines = [line for line in markdown_text.split('\n') if line.strip()]
    usecase_index = 0
    for i in range(len(lines) - 1):
        if lines[i].startswith('#') and not lines[i + 1].startswith('#') and usecase_index < len(usecase):
            lines[i] = usecase[usecase_index]
            usecase_index += 1
    return '\n'.join(lines)


def add_star_for_head(markdown_text):
    lines = markdown_text.split('\n')
    new_lines = []
    i = 0
    while i < len(lines):
        current_line = lines[i]
        if i + 1 < len(lines):
            next_line = lines[i + 1]
            if current_line.startswith('#') and next_line.startswith('#'):
                # 若当前行和下一行均为标题，在当前行标题内容前加*
                if ' ' in current_line:
                    index = current_line.index(' ')
                    new_lines.append(current_line[:index + 1] + '*' + current_line[index + 1:])
                else:
                    new_lines.append(current_line + '*')
            else:
                new_lines.append(current_line)
        else:
            new_lines.append(current_line)
        i = i + 1
    return '\n'.join(new_lines)        

def format_require_imgs(text, require_url=None):
    """
    从文本中提取所有符合要求的图片链接并替换为下载链接
    参数:
        text: 要搜索的文本字符串
        require_url: 需求URL，用于获取图片下载链接
    返回:
        处理后的文本和图片下载链接列表
    """
    # 正则表达式匹配以/tfl开头，以.png/.jpg/.jpeg结尾的链接
    pattern = r'/tfl[^\s]*?\.(?:png|jpg|jpeg)'
    matches = re.findall(pattern, text, re.IGNORECASE)
    
    if not matches or not require_url:
        return text, matches
    
    # 获取图片下载链接并替换原文
    processed_text = text
    download_urls = []
    for img_url in matches:
        download_url = get_img_download_url(require_url, img_url)
        if download_url:
            # 替换原文中的图片链接
            processed_text = processed_text.replace(img_url, download_url)
            download_urls.append(download_url)
    
    return processed_text, download_urls


def extract_image_links(text):
    """
    从文本中提取所有符合要求的图片链接
    参数:
        text: 要搜索的文本字符串
    返回:
        匹配到的图片链接列表
    """
    # 正则表达式匹配以/tfl开头，以.png/.jpg/.jpeg结尾的链接
    pattern = r'/tfl[^\s]*?\.(?:png|jpg|jpeg)'
    matches = re.findall(pattern, text, re.IGNORECASE)
    return matches




def ai_case(history, require_url):
    # 获取需求详情
    require,require_name = get_rquire(require_url)
    require, require_name = get_rquire(require_url)
    # 生成用例文件
    result_path=gen_case_path(require_name)
    history.append({"role": "user", "content": f"帮我分析需求文档，生成用例集～"})
    yield history,result_path
    # 处理需求图片
    img_require , download_urls = format_require_imgs(require, require_url) 
    if require is None:
       history.append({"role": "assistant", "content": "需求链接错误，请重新输入"})
       yield history,result_path
    history.append({"role": "assistant", "content": f"正在分析: {require_name}>>>"})
    history.append({"role": "assistant", "content": f" {img_require}\n"})
    logger.info(f"需求文档: {require}\n\n")
    yield history, result_path
    
    if len(require) > 1000:
        # 提取模块标题
        titles = chat_stream(extract_document_title(require)) 
        titles_list = ast.literal_eval(titles.choices[0].message.content)
        logger.info(f"提取模块标题: {titles_list}\n\n")
        splited_require_list = split_document_by_titles(require, titles_list)
        logger.info(f"拆分需求文档: {splited_require_list}\n\n")
    else:
        splited_require_list = [{"title": require_name, "content": require}]
    case_markdown = ""
    for item in splited_require_list:
        simple_require = json.dumps(item, ensure_ascii=False, indent=4)      
        
        
        # 判断是否需要生成用例
        is_need =  chat_stream(is_need_gen_case(simple_require), temperature=0.2, max_tokens=4086)
        logger.info(is_need_gen_case(simple_require))
        if is_need.choices[0].message.content == "否":
            logger.info(f"否\n")
            continue
        logger.info(f"是\n")
        
        # 获取图片
        img_urls = extract_image_links(simple_require)
        logger.info(f"提取图片: {img_urls}\n\n")
        img_download_urls = []
        for img_url in img_urls:
            download_url  = get_img_download_url(require_url, img_url)
            img_download_urls.append(download_url)
        logger.info(f"该模块的图片列表: {img_download_urls}\n\n")
        
        
        # 生成用例
        case_prompt = gen_case_prompt(simple_require)
        full_response = ""
        full_response += "\n# 正在生成用例>>>\n\n"
        history.append({"role": "assistant", "content": full_response})
        yield history, result_path
        gen_case_res = hunyuan_vision(case_prompt, img_download_urls, is_stream=True, temperature=0.2, max_tokens=12288)
        case_content = ""
        for chunk in gen_case_res:
            if chunk.choices[0].delta.content:
                full_response += chunk.choices[0].delta.content
                case_content += chunk.choices[0].delta.content
                history[-1]["content"] = full_response
                yield history, result_path
        case_markdown = case_markdown + "\n" + case_content
    logger.info(f"生成用例结果:\n{case_markdown}")
    
    
    full_response +="\n# 正在评估用例优先级>>>\n\n"
    history.append({"role": "assistant", "content": full_response})
    yield history, result_path
    
    # 生成优先级
    use_cases = get_markdown_usecase(case_markdown)
    level_prompt = case_level_prompt(use_cases)
    print(f"优先级prompt:{level_prompt}")
    levels = chat_stream(level_prompt, temperature=0.1, max_tokens=12200).choices[0].message.content
    print(levels)
    levels=json.loads(levels)
    

    # 将优先级附加到标题字符串后面
    use_cases = [f"{title}#{0 if item['level'] == '高' else 1 if item['level'] == '较高' else 2}" for title, item in zip(use_cases, levels)]
    case_markdown = update_markdown_usecase(case_markdown, use_cases)
    # 规范用例
    case_markdown = clean_markdown_labels(case_markdown)
    case_markdown = add_star_for_head(case_markdown)
    # 生成xmind
    markdown_to_xmind(case_markdown, result_path, require_name)
    history.append({"role": "assistant", "content": f"用例生成完成，请查看生成结果"})
    yield history, result_path 


with gr.Blocks(title="用例生成系统", theme=gr.themes.Soft(font=[gr.themes.GoogleFont("Inconsolata"), "Arial", "sans-serif"])) as demo:
    gr.Markdown("# 🧠 AI用例生成系统")
    gr.Markdown("上传需求URL，获取推荐用例")
    with gr.Row():
        with gr.Column(scale=1):
            url = gr.Textbox(
                label="TAPD需求链接",
                placeholder="请输入完整的TAPD需求URL...",
                lines=2
            )
            submit_btn = gr.Button("开始评审", variant="primary")
            # 示例输入
            gr.Examples(
                examples=[
                    ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122143306"],
                    ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"]
                ],
                inputs=[url],
            )
            
        with gr.Column(scale=2):
            chatbot = gr.Chatbot(
                label="生成用例",
                avatar_images=("data/user.jpeg", "data/bot.jpeg"),
                height=700,
                show_share_button=True,
                type="messages",
            )
            result = gr.File(
                label="生成用例",
                height=50
            )
    


    submit_btn.click(
        fn=ai_case,
        inputs=[chatbot, url],
        outputs=[chatbot, result]
    )


# if __name__ == "__main__":
#     demo.queue(default_concurrency_limit=5).launch(
#         server_name="0.0.0.0",
#         server_port=8040,
#         show_api=False
#     )
