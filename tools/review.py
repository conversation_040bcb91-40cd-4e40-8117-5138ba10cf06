import pandas as pd
import gradio as gr
import re
from openai import OpenAI
import os.path
from dotenv import load_dotenv
from io import StringIO
from datetime import datetime
import os
from utils.logger.logger import Logging
from utils.llm.prompt import review_prompt,check_case_prompt,analize_check_points_prompt
from utils.tapd import HTMLProcessor, TAPDClient,AppConfig
from utils.xmind.xmind_parser import xmind_parser
# 加载模型环境
load_dotenv()
logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_key'))
def read_excel_columns(file_path):
    # 读取 Excel 文件
    df = pd.read_excel(file_path)

    # 获取列的表头
    c_header = df.columns[2]  # C 列
    l_header = df.columns[11] # L 列
    m_header = df.columns[12] # M 列

    # 存储输出内容的列表
    output_list = []

    # 遍历每一行并存储格式化内容
    for index, row in df.iterrows():
        output = {
            '序号': index + 1,
            c_header: row[c_header],
            l_header: row[l_header],
            m_header: row[m_header]
        }
        output_list.append(output)

    return output_list

def gen_output_path(usecase_file):  
    output_dir="data/review_result"
    file_name = os.path.splitext(os.path.basename(usecase_file))[0]
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    # 生成唯一的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(output_dir, f"{file_name}_{timestamp}.xlsx")
    if not os.path.exists(output_path):
        # 如果文件不存在，创建一个新文件
        with open(output_path, "w") as file:
            print(f"File '{output_path}' created.")
    else:
        print(f"File '{output_path}' already exists.")
    return output_path


def csv_string_to_excel(output_path, csv_string):
    """
    将 CSV 格式的字符串转换为 Excel 文件并保存
    :param csv_string: CSV 格式的字符串
    :param output_dir: 输出目录，默认为 "output"
    :return: 生成的 Excel 文件路径
    """
    print("正在转换 CSV 字符串为 Excel 文件...",csv_string)
    # 将 CSV 字符串转换为 DataFrame
    try:
        # 使用 pandas 内置方法解析 CSV 字符串
        csv_file = StringIO(csv_string)
        df = pd.read_csv(csv_file)
        
        # 新增“是否采纳”列，默认值为空字符串
        df['是否采纳'] = ''
        
        # 使用 ExcelWriter 的覆盖模式保存
        with pd.ExcelWriter(output_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, index=False)
                        # 获取工作表对象
            worksheet = writer.sheets['Sheet1']
            
            # 设置列宽
            worksheet.column_dimensions['A'].width = 15  # 第一列宽度
            worksheet.column_dimensions['B'].width = 80  # 第二列宽度
            worksheet.column_dimensions['C'].width = 10  # 第三列宽度
            # worksheet.column_dimensions['D'].width = 60  # 第四列宽度
            # worksheet.column_dimensions['E'].width = 10  # 第四列宽度
            # worksheet.column_dimensions['F'].width = 20  # 第四列宽度

        return output_path
    except Exception as e:
        print(f"转换失败: {e}")
        return None


import csv
from io import StringIO

def csv_to_markdown(csv_string):
    """
    将 CSV 字符串转换为 Markdown 表格格式。

    :param csv_string: 包含 CSV 数据的字符串
    :return: Markdown 格式的表格字符串
    """
    # 使用 StringIO 将字符串转换为文件对象
    f = StringIO(csv_string)
    reader = csv.reader(f)
    
    # 读取所有行
    rows = list(reader)
    
    if not rows:
        print("CSV 字符串为空，无法转换为 Markdown 表格。")
        return ""
    
    # 构建表头
    header = rows[0]
    markdown_table = "| " + " | ".join(header) + " |\n"
    markdown_table += "| " + " | ".join(["---"] * len(header)) + " |\n"
    
    # 构建表格内容
    for row in rows[1:]:
        markdown_table += "| " + " | ".join(row) + " |\n"
    
    return markdown_table
    
def get_file_type(file_path):
    """
    判断文件类型是excel还是xmind
    """
    file_extension = os.path.splitext(file_path)[1].lower()
    if file_extension in ['.xlsx', '.xls']:
        return 'excel'
    elif file_extension == '.xmind':
        return 'xmind'
    else:
        raise ValueError(f"不支持的文件类型: {file_extension}")


def get_usecase(usecase_path):
     # 处理用例文件
    file_type = get_file_type(usecase_path)
    if file_type == 'excel':
        usecase = read_excel_columns(usecase_path)

    elif file_type == 'xmind':
        usecase = xmind_parser(usecase_path)
    else:
        usecase = None
    return usecase

def get_rquire(url):
    pattern = r"/tapd_fe/(\d+)/story/detail/(\d+)"  # 匹配两个连续的数值
    match = re.search(pattern, url)
    # 处理产品需求
    if match:
        workspace_id = match.group(1)  
        story_id = match.group(2)
        config = AppConfig()
        client = TAPDClient(auth_token=config.auth_token)
        # 获取数据
        description = client.get_story_description(workspace_id,story_id)
        # 转换处理
        processor = HTMLProcessor(
            excluded_tags=config.excluded_tags,
            markdown_config=config.markdown_config
        )
        require = processor.convert_to_markdown(description)
        return  require
    else:
        return None
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_key')) 
def chat_stream(prompt):
    
    response = hy_client.chat.completions.create(
        model="hunyuan-turbo",
        messages=[
            {"role": "user", "content": prompt }
        ],
        temperature=0.1,  # 添加温度参数
        max_tokens=12288,   # 添加最大令牌数
        stream=True,
    )
    print(prompt)

    return response

def ai_review(history,usecase_path, url):
    result_path = gen_output_path(usecase_path)
    history.append({"role": "user", "content": f"帮我完成用例评审～"})
    yield history,result_path
    usecase = get_usecase(usecase_path)
    require = get_rquire(url)
    if usecase is None:
       history.append({"role": "assistant", "content": "用例文件格式错误，请重新上传"})
       yield history,result_path
    if require is None:
       history.append({"role": "assistant", "content": "需求链接错误，请重新输入"})
       yield history,result_path
    if usecase is not None and require is not None:
        # prompt = review_prompt(usecase, require)
        # 生成检查点
        check_points_prompt = analize_check_points_prompt(require)
        print("生成检查点prompt:\n",check_points_prompt)
        full_response=""
        full_response+="\n# 正在基于 PRD 分析功能测试点>>>\n\n"
        history.append({"role": "assistant", "content": full_response})
        yield history,result_path
        check_points_content=""
        check_points = chat_stream(check_points_prompt)
        for chunk in check_points:
            if chunk.choices[0].delta.content:
                full_response += chunk.choices[0].delta.content
                check_points_content+=chunk.choices[0].delta.content
                history[-1]["content"] = full_response
                yield history,result_path 
        print("检查点:\n",check_points)
        
        # 生成覆盖缺失项
        review_prompt = check_case_prompt(require,usecase,check_points_content)
        print("生成评审prompt:\n",review_prompt)
        
        full_response+="\n\n# 正在检查用例覆盖缺失项>>>\n\n"
        history[-1]["content"] = full_response
        yield history,result_path
        
        review_res = chat_stream(review_prompt)
        review_content=""
        for chunk in review_res:
            if chunk.choices[0].delta.content:
                full_response += chunk.choices[0].delta.content
                review_content += chunk.choices[0].delta.content
                history[-1]["content"] = full_response
                yield history,result_path 
        yield history,result_path
        
        # 生成评审报告
        full_response+="\n\n# 正在生成评审报告>>>\n\n"
        history[-1]["content"] = full_response
        yield history,result_path
        
        markdonw_text = csv_to_markdown(review_content)
        history.append({"role": "assistant", "content": markdonw_text})
        yield history,result_path
        result_path = csv_string_to_excel(result_path,review_content)
        yield history,result_path

        
# if __name__ == "__main__":
#     demo.queue(default_concurrency_limit=5).launch(
#         server_name="0.0.0.0",
#         server_port=8006,
#         show_api=False
#     )
